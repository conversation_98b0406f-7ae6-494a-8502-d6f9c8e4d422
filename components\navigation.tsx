"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Menu as MenuIcon, X } from "lucide-react";

export function Navigation() {
  const [isMobileOpen, setIsMobileOpen] = React.useState(false);

  const navItems = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Programs", href: "/programs" },
    { name: "Contact", href: "/contact" },
  ];

  return (
    <>
      {/* Desktop Navigation */}
      <motion.div
        className="fixed top-6 inset-x-0 max-w-4xl mx-auto z-50 hidden md:block"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <motion.nav
          className="bg-white/90 backdrop-blur-md border border-gray-200/50 rounded-2xl shadow-lg px-8 py-4"
          whileHover={{ y: -2 }}
          transition={{ duration: 0.2 }}
        >
          <div className="flex items-center justify-between">
            {/* Logo */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <Link href="/" className="flex items-center space-x-3">
                <Image
                  src="/images/wlc-logo.png"
                  alt="WLC Academy"
                  width={32}
                  height={32}
                  className="rounded-md"
                />
                <span className="text-lg font-semibold text-[#07243C]">WLC Academy</span>
              </Link>
            </motion.div>

            {/* Navigation Links */}
            <div className="flex items-center space-x-8">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className="relative text-gray-700 hover:text-[#07243C] font-medium transition-colors duration-200 group"
                  >
                    {item.name}
                    <motion.div
                      className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#F3CC5C] group-hover:w-full transition-all duration-300"
                      whileHover={{ width: "100%" }}
                    />
                  </Link>
                </motion.div>
              ))}

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  className="bg-[#07243C] text-white hover:bg-[#0a2d47] px-6 py-2 rounded-md font-medium shadow-md hover:shadow-lg transition-all duration-200"
                >
                  Enroll Now
                </Button>
              </motion.div>
            </div>
          </div>
        </motion.nav>
      </motion.div>

      {/* Mobile Navigation */}
      <motion.nav
        className="fixed top-0 w-full bg-white/95 backdrop-blur-sm z-50 border-b border-gray-100 md:hidden"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/images/wlc-logo.png"
                alt="WLC Academy"
                width={32}
                height={32}
                className="rounded-md"
              />
              <span className="text-xl font-medium text-[#07243C]">WLC Academy</span>
            </Link>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileOpen(!isMobileOpen)}
              className="p-2"
            >
              {isMobileOpen ? <X className="h-6 w-6" /> : <MenuIcon className="h-6 w-6" />}
            </Button>
          </div>

          {isMobileOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-100 rounded-md"
            >
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-md transition-colors duration-200"
                  onClick={() => setIsMobileOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <div className="px-3 py-2">
                <Button className="w-full bg-[#07243C] text-white hover:bg-[#0a2d47] rounded-md">
                  Enroll Now
                </Button>
              </div>
            </motion.div>
          )}
        </div>
      </motion.nav>
    </>
  );
}