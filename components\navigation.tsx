"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { Menu, MenuItem, HoveredLink } from "../src/components/ui/navbar-menu";
import { Button } from "@/components/ui/button";
import { Menu as MenuIcon, X } from "lucide-react";

export function Navigation() {
  const [active, setActive] = React.useState<string | null>(null);
  const [isMobileOpen, setIsMobileOpen] = React.useState(false);

  return (
    <>
      {/* Desktop Navigation */}
      <motion.div
        className="fixed top-4 inset-x-0 max-w-2xl mx-auto z-50 hidden md:block"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <Menu setActive={setActive}>
          <Link href="/" className="flex items-center space-x-2 mr-4">
            <Image
              src="/images/wlc-logo.png"
              alt="WLC Academy"
              width={24}
              height={24}
              className="rounded-md"
            />
            <span className="text-sm font-medium text-[#07243C]">WLC Academy</span>
          </Link>

          <MenuItem setActive={setActive} active={active} item="Home">
            <div className="flex flex-col space-y-4 text-sm">
              <HoveredLink href="/">Welcome</HoveredLink>
              <HoveredLink href="/#about">About Us</HoveredLink>
              <HoveredLink href="/#gallery">Gallery</HoveredLink>
            </div>
          </MenuItem>

          <MenuItem setActive={setActive} active={active} item="Programs">
            <div className="text-sm grid grid-cols-1 gap-4 p-4">
              <div>
                <h4 className="text-[#07243C] font-medium mb-2">Afterschool Program</h4>
                <p className="text-neutral-700 text-xs max-w-[200px]">
                  Year-round academic support and enrichment activities for ages 5-14
                </p>
                <HoveredLink href="/programs" className="text-[#F3CC5C] hover:text-[#07243C] text-xs">
                  Learn More →
                </HoveredLink>
              </div>
            </div>
          </MenuItem>

          <MenuItem setActive={setActive} active={active} item="About">
            <div className="flex flex-col space-y-4 text-sm">
              <HoveredLink href="/about">Our Story</HoveredLink>
              <HoveredLink href="/about#team">Our Team</HoveredLink>
              <HoveredLink href="/about#mission">Mission & Vision</HoveredLink>
            </div>
          </MenuItem>

          <MenuItem setActive={setActive} active={active} item="Contact">
            <div className="flex flex-col space-y-4 text-sm">
              <HoveredLink href="/contact">Get in Touch</HoveredLink>
              <HoveredLink href="/contact#location">Location</HoveredLink>
              <HoveredLink href="/contact#hours">Hours</HoveredLink>
            </div>
          </MenuItem>

          <Button
            className="bg-[#07243C] text-white hover:bg-[#0a2d47] text-sm px-4 py-2 rounded-md ml-4"
            size="sm"
          >
            Enroll Now
          </Button>
        </Menu>
      </motion.div>

      {/* Mobile Navigation */}
      <motion.nav
        className="fixed top-0 w-full bg-white/95 backdrop-blur-sm z-50 border-b border-gray-100 md:hidden"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/images/wlc-logo.png"
                alt="WLC Academy"
                width={32}
                height={32}
                className="rounded-md"
              />
              <span className="text-xl font-medium text-[#07243C]">WLC Academy</span>
            </Link>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileOpen(!isMobileOpen)}
              className="p-2"
            >
              {isMobileOpen ? <X className="h-6 w-6" /> : <MenuIcon className="h-6 w-6" />}
            </Button>
          </div>

          {isMobileOpen && (
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-100 rounded-md">
              <Link
                href="/"
                className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-md"
                onClick={() => setIsMobileOpen(false)}
              >
                Home
              </Link>
              <Link
                href="/about"
                className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-md"
                onClick={() => setIsMobileOpen(false)}
              >
                About
              </Link>
              <Link
                href="/programs"
                className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-md"
                onClick={() => setIsMobileOpen(false)}
              >
                Programs
              </Link>
              <Link
                href="/contact"
                className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-md"
                onClick={() => setIsMobileOpen(false)}
              >
                Contact
              </Link>
              <div className="px-3 py-2">
                <Button className="w-full bg-[#07243C] text-white hover:bg-[#0a2d47] rounded-md">
                  Enroll Now
                </Button>
              </div>
            </div>
          )}
        </div>
      </motion.nav>
    </>
  );
}