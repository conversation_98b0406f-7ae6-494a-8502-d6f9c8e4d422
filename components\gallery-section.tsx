"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

// Real gallery images from the public/images/gallery folder
const galleryImages = [
  "/images/gallery/FullSizeRender.jpg",
  "/images/gallery/FullSizeRender(1).jpg",
  "/images/gallery/FullSizeRender(2).jpg",
  "/images/gallery/FullSizeRender(3).jpg",
  "/images/gallery/FullSizeRender(4).jpg",
  "/images/gallery/FullSizeRender(5).jpg",
  "/images/gallery/FullSizeRender(6).jpg",
  "/images/gallery/FullSizeRender(7).jpg",
  "/images/gallery/FullSizeRender(8).jpg",
  "/images/gallery/FullSizeRender(9).jpg",
  "/images/gallery/FullSizeRender(10).jpg",
  "/images/gallery/IMG_5766.JPG",
  "/images/gallery/IMG_6570.jpg",
  "/images/gallery/IMG_6585.jpg",
  "/images/gallery/IMG_6601.jpg",
  "/images/gallery/IMG_6650.jpg",
  "/images/gallery/IMG_6651.jpg",
  "/images/gallery/IMG_6682.jpg",
  "/images/gallery/IMG_6686.jpg",
  "/images/gallery/0dd790e6ca0612d925de0e5f9dec4f39.JPG",
  "/images/gallery/51f8def3fcf2d68557ea5ccf682e5d75.JPG",
];

const galleryImageSets = [
  {
    layout: "masonry",
    title: "Learning Excellence",
    description: "Capturing moments of academic achievement and growth",
    images: galleryImages.slice(0, 6)
  },
  {
    layout: "grid",
    title: "Community Spirit",
    description: "Celebrating our vibrant school community",
    images: galleryImages.slice(6, 12)
  },
  {
    layout: "vertical",
    title: "Innovation & Creativity",
    description: "Showcasing student creativity and innovation",
    images: galleryImages.slice(12, 18)
  }
];

export function GallerySection() {
  const [currentSet, setCurrentSet] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-scroll functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentSet((prev) => (prev + 1) % galleryImageSets.length);
    }, 6000); // Change every 6 seconds

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const currentSetData = galleryImageSets[currentSet];
  const currentImages = currentSetData.images;
  const currentLayout = currentSetData.layout;

  const nextSet = () => {
    setCurrentSet((prev) => (prev + 1) % galleryImageSets.length);
    setIsAutoPlaying(false);
  };

  const prevSet = () => {
    setCurrentSet((prev) => (prev - 1 + galleryImageSets.length) % galleryImageSets.length);
    setIsAutoPlaying(false);
  };

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-6xl mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
            Gallery
          </div>
          <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
            Moments of Excellence
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Capturing the spirit of learning, growth, and achievement at WLC Academy
          </p>
        </motion.div>

        {/* Gallery Navigation */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h3 className="text-xl font-medium text-gray-900">{currentSetData.title}</h3>
            <p className="text-gray-600 text-sm">{currentSetData.description}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={prevSet}
              className="p-2"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={nextSet}
              className="p-2"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Dynamic Image Grid with Auto-Scroll */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSet}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.8 }}
            className="h-[600px]"
          >
            {/* Layout 1: Masonry - Large Left + Small Right Grid */}
            {currentLayout === "masonry" && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
                <div className="md:col-span-2">
                  <Card className="relative h-full overflow-hidden group">
                    <Image
                      src={currentImages[0]}
                      alt="Gallery image"
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Card>
                </div>
                <div className="grid grid-rows-5 gap-3">
                  {currentImages.slice(1).map((image, index) => (
                    <Card key={index} className="relative overflow-hidden group">
                      <Image
                        src={image}
                        alt={`Gallery image ${index + 2}`}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Layout 2: Grid - Top Large + Bottom Row */}
            {currentLayout === "grid" && (
              <div className="grid grid-rows-3 gap-6 h-full">
                <div className="row-span-2">
                  <Card className="relative h-full overflow-hidden group">
                    <Image
                      src={currentImages[0]}
                      alt="Gallery image"
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Card>
                </div>
                <div className="grid grid-cols-5 gap-3">
                  {currentImages.slice(1).map((image, index) => (
                    <Card key={index} className="relative overflow-hidden group">
                      <Image
                        src={image}
                        alt={`Gallery image ${index + 2}`}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Layout 3: Vertical - Mixed Grid */}
            {currentLayout === "vertical" && (
              <div className="grid grid-cols-3 gap-6 h-full">
                <div className="grid grid-rows-2 gap-6">
                  {currentImages.slice(1, 3).map((image, index) => (
                    <Card key={index} className="relative overflow-hidden group">
                      <Image
                        src={image}
                        alt={`Gallery image ${index + 2}`}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Card>
                  ))}
                </div>
                <div>
                  <Card className="relative h-full overflow-hidden group">
                    <Image
                      src={currentImages[0]}
                      alt="Gallery image"
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Card>
                </div>
                <div className="grid grid-rows-3 gap-4">
                  {currentImages.slice(3, 6).map((image, index) => (
                    <Card key={index} className="relative overflow-hidden group">
                      <Image
                        src={image}
                        alt={`Gallery image ${index + 4}`}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-[#07243C]/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Gallery Indicators */}
        <div className="flex justify-center items-center mt-12 space-x-4">
          <div className="flex space-x-2">
            {galleryImageSets.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentSet(index);
                  setIsAutoPlaying(false);
                }}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSet
                    ? 'bg-[#F3CC5C] scale-110'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Gallery Info */}
        <div className="text-center mt-8">
          <p className="text-gray-600">
            Showcasing moments of learning, achievement, and community at WLC Academy
          </p>
          <p className="text-sm text-gray-500 mt-2">
            {isAutoPlaying ? "Gallery automatically updates every 6 seconds" : "Auto-play paused"}
          </p>
        </div>
      </div>
    </section>
  );
}
