"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { Spotlight } from "@/components/ui/spotlight";
import { ArrowRight, Play } from "lucide-react";

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-b from-white via-gray-50/20 to-white overflow-hidden">
      {/* Background Effects */}
      <Spotlight className="-top-40 left-0 md:left-60 md:-top-20" fill="#F3CC5C" />
      <BackgroundBeams />

      {/* Yellow Background Effects */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-[#F3CC5C]/[0.03] via-transparent to-[#07243C]/[0.02]"
        animate={{
          opacity: [0.4, 0.7, 0.4],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Additional Yellow Accent Effects */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#F3CC5C]/5 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-[#07243C]/5 rounded-full blur-3xl"
        animate={{
          scale: [1.2, 1, 1.2],
          opacity: [0.2, 0.4, 0.2],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />

      {/* Floating Yellow Elements */}
      <motion.div
        className="absolute top-1/3 right-1/5 w-2 h-2 bg-[#F3CC5C] rounded-full opacity-60"
        animate={{
          y: [0, -20, 0],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className="absolute bottom-1/3 left-1/5 w-1 h-1 bg-[#F3CC5C] rounded-full opacity-40"
        animate={{
          y: [0, -15, 0],
          opacity: [0.4, 0.8, 0.4],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
      />

      {/* Geometric Yellow Accents */}
      <motion.div
        className="absolute top-1/5 right-1/3 w-4 h-4 border border-[#F3CC5C]/30 opacity-50"
        animate={{
          rotate: [0, 180, 360],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className="absolute bottom-1/5 left-1/3 w-3 h-3 border border-[#07243C]/20 rounded-full opacity-40"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.4, 0.7, 0.4],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3,
        }}
      />

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center py-32">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-8 py-4 bg-[#F3CC5C]/10 border border-[#F3CC5C]/20 rounded-full text-sm font-medium text-[#07243C] mb-16 backdrop-blur-sm"
          >
            <span className="w-2 h-2 bg-[#F3CC5C] rounded-full mr-3 animate-pulse"></span>
            Excellence in Afterschool Education
          </motion.div>

          <div className="text-7xl md:text-8xl lg:text-9xl font-extralight text-gray-900 mb-12 leading-[0.85] tracking-tight">
            <TextGenerateEffect
              words="Nurturing Growth After School"
              className="text-7xl md:text-8xl lg:text-9xl font-extralight text-gray-900 leading-[0.85] tracking-tight"
            />
          </div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-xl md:text-2xl text-gray-500 leading-relaxed max-w-4xl mx-auto mb-6 font-light"
          >
            A comprehensive year-round afterschool program providing academic support,
            enrichment activities, and a safe learning environment for students ages 5-14.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="text-base text-gray-400 max-w-2xl mx-auto"
          >
            Monday - Friday • 3:00 PM - 6:00 PM • Year-Round Program
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="flex flex-col sm:flex-row items-center justify-center gap-6"
        >
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="group"
          >
            <Button
              size="lg"
              className="bg-[#07243C] text-white hover:bg-[#0a2d47] px-12 py-5 text-lg font-medium group relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-md"
            >
              <span className="relative z-10 flex items-center">
                Enroll Today
                <ArrowRight className="ml-3 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </span>
            </Button>
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              variant="outline"
              size="lg"
              className="text-[#07243C] border-[#07243C]/30 hover:bg-[#07243C]/5 hover:border-[#07243C] px-12 py-5 text-lg font-medium group backdrop-blur-sm bg-white/50 rounded-md"
            >
              <Play className="mr-3 h-5 w-5" />
              Learn More
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
