"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { Spotlight } from "@/components/ui/spotlight";
import { FloatingElements } from "@/components/ui/floating-elements";
import { ScrollIndicator } from "@/components/ui/scroll-indicator";
import { ArrowRight, Play } from "lucide-react";

const stats = [
  { number: "500+", label: "Students" },
  { number: "50+", label: "Expert Teachers" },
  { number: "15+", label: "Years Experience" },
  { number: "98%", label: "Success Rate" },
];

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-b from-white via-gray-50/20 to-white overflow-hidden">
      <Spotlight className="-top-40 left-0 md:left-60 md:-top-20" fill="#F3CC5C" />
      <BackgroundBeams />
      <FloatingElements />

      {/* Subtle animated background overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-[#F3CC5C]/[0.02] via-transparent to-[#07243C]/[0.02]"
        animate={{
          opacity: [0.5, 0.8, 0.5],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center px-6 py-3 bg-[#F3CC5C]/10 border border-[#F3CC5C]/20 rounded-full text-sm font-medium text-[#07243C] mb-12 backdrop-blur-sm"
          >
            <span className="w-2 h-2 bg-[#F3CC5C] rounded-full mr-3 animate-pulse"></span>
            Excellence in Afterschool Education
          </motion.div>

          <div className="text-6xl md:text-7xl lg:text-8xl font-extralight text-gray-900 mb-8 leading-[0.9] tracking-tight">
            <TextGenerateEffect
              words="Nurturing Growth After School"
              className="text-6xl md:text-7xl lg:text-8xl font-extralight text-gray-900 leading-[0.9] tracking-tight"
            />
          </div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-xl md:text-2xl text-gray-500 leading-relaxed max-w-4xl mx-auto mb-4 font-light"
          >
            A comprehensive year-round afterschool program providing academic support,
            enrichment activities, and a safe learning environment for students ages 5-14.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="text-base text-gray-400 max-w-2xl mx-auto"
          >
            Monday - Friday • 3:00 PM - 6:00 PM • Year-Round Program
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-20"
        >
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="group"
          >
            <Button
              size="lg"
              className="bg-[#07243C] text-white hover:bg-[#0a2d47] px-10 py-4 text-lg font-medium group relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <span className="relative z-10 flex items-center">
                Enroll Today
                <ArrowRight className="ml-3 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-[#07243C] to-[#0a2d47] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Button>
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              variant="outline"
              size="lg"
              className="text-[#07243C] border-[#07243C]/30 hover:bg-[#07243C]/5 hover:border-[#07243C] px-10 py-4 text-lg font-medium group backdrop-blur-sm bg-white/50"
            >
              <Play className="mr-3 h-5 w-5" />
              Learn More
            </Button>
          </motion.div>
        </motion.div>

        {/* Stats - Commented out for cleaner design */}
        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto pt-8 border-t border-gray-100"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
              className="text-center"
            >
              <div className="text-2xl md:text-3xl font-medium text-gray-900 mb-1">
                {stat.number}
              </div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div> */}
      </div>

      <ScrollIndicator />
    </section>
  );
}
